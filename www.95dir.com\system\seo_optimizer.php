<?php
/**
 * SEO优化工具 - 解决Google索引问题
 * 处理重复页面、canonical标签、重定向等SEO问题
 */

if (!defined('IN_IWEBDIR')) {
    define('IN_IWEBDIR', true);
}

require_once dirname(__FILE__) . '/../config.php';
require_once dirname(__FILE__) . '/../module/common.php';

class SEOOptimizer {
    
    private $DB;
    private $options;
    
    public function __construct() {
        global $DB, $options;
        $this->DB = $DB;
        $this->options = $options;
    }
    
    /**
     * 检查并修复重复页面问题
     */
    public function fixDuplicatePages() {
        $issues = [];
        
        // 检查重复的网站页面
        $query = $this->DB->query("
            SELECT web_id, web_name, web_url, COUNT(*) as count 
            FROM " . $this->DB->table('websites') . " 
            WHERE web_status = 1 
            GROUP BY web_url 
            HAVING count > 1
        ");
        
        while ($row = $this->DB->fetch_array($query)) {
            $issues[] = [
                'type' => 'duplicate_website',
                'url' => $row['web_url'],
                'count' => $row['count']
            ];
        }
        
        return $issues;
    }
    
    /**
     * 生成正确的canonical URL
     */
    public function generateCanonicalUrl($mod, $params = []) {
        $base_url = rtrim($this->options['site_url'], '/');
        
        switch ($mod) {
            case 'siteinfo':
                if (isset($params['wid'])) {
                    return $base_url . '/siteinfo/' . $params['wid'] . '.html';
                }
                break;
                
            case 'artinfo':
                if (isset($params['wid'])) {
                    return $base_url . '/artinfo/' . $params['wid'] . '.html';
                }
                break;
                
            case 'webdir':
                if (isset($params['cid'])) {
                    $cate_info = $this->getCategoryInfo($params['cid']);
                    $url = $base_url . '/webdir/' . $cate_info['cate_dir'] . '/' . $params['cid'];
                    if (isset($params['page']) && $params['page'] > 1) {
                        $url .= '-' . $params['page'];
                    }
                    return $url . '.html';
                }
                break;
                
            case 'article':
                if (isset($params['cid'])) {
                    $cate_info = $this->getCategoryInfo($params['cid']);
                    $url = $base_url . '/article/' . $cate_info['cate_dir'] . '/' . $params['cid'];
                    if (isset($params['page']) && $params['page'] > 1) {
                        $url .= '-' . $params['page'];
                    }
                    return $url . '.html';
                }
                break;
                
            case 'search':
                $type = isset($params['type']) ? $params['type'] : 'name';
                $query = isset($params['query']) ? $params['query'] : '';
                $url = $base_url . '/search/' . $type . '/' . urlencode($query);
                if (isset($params['page']) && $params['page'] > 1) {
                    $url .= '-' . $params['page'];
                }
                return $url . '.html';
                
            default:
                return $base_url . '/';
        }
        
        return $base_url . '/';
    }
    
    /**
     * 获取分类信息
     */
    private function getCategoryInfo($cid) {
        $query = $this->DB->query("SELECT cate_dir, cate_name FROM " . $this->DB->table('categories') . " WHERE cate_id = " . intval($cid));
        return $this->DB->fetch_array($query);
    }
    
    /**
     * 检查页面状态码
     */
    public function checkPageStatus($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; SEO-Optimizer/1.0)');
        
        curl_exec($ch);
        $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $redirect_count = curl_getinfo($ch, CURLINFO_REDIRECT_COUNT);
        curl_close($ch);
        
        return [
            'status_code' => $status_code,
            'redirect_count' => $redirect_count
        ];
    }
    
    /**
     * 生成结构化数据
     */
    public function generateStructuredData($type, $data) {
        switch ($type) {
            case 'website':
                return [
                    '@context' => 'https://schema.org',
                    '@type' => 'WebSite',
                    'name' => $data['web_name'],
                    'url' => $data['web_url'],
                    'description' => $data['web_intro'],
                    'potentialAction' => [
                        '@type' => 'SearchAction',
                        'target' => $this->options['site_url'] . '?mod=search&type=name&query={search_term_string}',
                        'query-input' => 'required name=search_term_string'
                    ]
                ];
                
            case 'article':
                return [
                    '@context' => 'https://schema.org',
                    '@type' => 'Article',
                    'headline' => $data['art_title'],
                    'description' => $data['art_intro'],
                    'datePublished' => date('c', $data['art_ctime']),
                    'dateModified' => date('c', $data['art_mtime']),
                    'author' => [
                        '@type' => 'Organization',
                        'name' => $this->options['site_name']
                    ]
                ];
        }
        
        return null;
    }
    
    /**
     * 清理重复内容
     */
    public function cleanDuplicateContent() {
        $cleaned = 0;
        
        // 清理重复的网站记录（保留最新的）
        $query = $this->DB->query("
            DELETE w1 FROM " . $this->DB->table('websites') . " w1
            INNER JOIN " . $this->DB->table('websites') . " w2 
            WHERE w1.web_id < w2.web_id 
            AND w1.web_url = w2.web_url 
            AND w1.web_status = w2.web_status
        ");
        
        $cleaned += $this->DB->affected_rows();
        
        return $cleaned;
    }
}

// 如果直接访问此文件，执行SEO检查
if (basename($_SERVER['PHP_SELF']) == 'seo_optimizer.php') {
    $optimizer = new SEOOptimizer();
    
    header('Content-Type: application/json; charset=utf-8');
    
    $action = isset($_GET['action']) ? $_GET['action'] : 'check';
    
    switch ($action) {
        case 'check':
            $issues = $optimizer->fixDuplicatePages();
            echo json_encode([
                'status' => 'success',
                'issues' => $issues,
                'count' => count($issues)
            ]);
            break;
            
        case 'clean':
            $cleaned = $optimizer->cleanDuplicateContent();
            echo json_encode([
                'status' => 'success',
                'cleaned' => $cleaned,
                'message' => "已清理 {$cleaned} 条重复记录"
            ]);
            break;
            
        case 'canonical':
            $mod = $_GET['mod'] ?? 'index';
            $params = $_GET;
            unset($params['action'], $params['mod']);
            
            $canonical = $optimizer->generateCanonicalUrl($mod, $params);
            echo json_encode([
                'status' => 'success',
                'canonical' => $canonical
            ]);
            break;
            
        default:
            echo json_encode([
                'status' => 'error',
                'message' => '未知操作'
            ]);
    }
}
?>
