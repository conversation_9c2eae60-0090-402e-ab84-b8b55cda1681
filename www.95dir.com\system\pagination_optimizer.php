<?php
/**
 * 分页URL优化工具
 * 专门处理类似 ?mod=webdir&cid=0&page=2 这样的分页URL问题
 */

if (!defined('IN_IWEBDIR')) {
    define('IN_IWEBDIR', true);
}

require_once dirname(__FILE__) . '/../config.php';
require_once dirname(__FILE__) . '/../module/common.php';

class PaginationOptimizer {
    
    private $DB;
    private $options;
    
    public function __construct() {
        global $DB, $options;
        $this->DB = $DB;
        $this->options = $options;
    }
    
    /**
     * 检查有问题的分页URL
     */
    public function checkProblematicUrls() {
        $issues = [];
        
        // 检查webdir分页问题
        $webdir_issues = $this->checkModulePagination('webdir');
        $issues = array_merge($issues, $webdir_issues);
        
        // 检查article分页问题
        $article_issues = $this->checkModulePagination('article');
        $issues = array_merge($issues, $article_issues);
        
        return $issues;
    }
    
    /**
     * 检查特定模块的分页问题
     */
    private function checkModulePagination($module) {
        $issues = [];
        
        // 检查全站分页（cid=0）的情况
        $table = ($module == 'webdir') ? 'websites' : 'articles';
        $status_field = ($module == 'webdir') ? 'web_status' : 'art_status';
        
        $total = $this->DB->get_count($this->DB->table($table), "$status_field = 1");
        $pages = ceil($total / 10); // 假设每页10条
        
        for ($page = 2; $page <= min($pages, 10); $page++) {
            $problematic_url = $this->options['site_url'] . "?mod=$module&cid=0&page=$page";
            $optimized_url = $this->generateOptimizedUrl($module, 0, $page);
            
            $issues[] = [
                'type' => 'pagination_cid_zero',
                'module' => $module,
                'page' => $page,
                'problematic_url' => $problematic_url,
                'optimized_url' => $optimized_url,
                'canonical_url' => $this->generateCanonicalUrl($module, 0, $page)
            ];
        }
        
        return $issues;
    }
    
    /**
     * 生成优化后的URL
     */
    public function generateOptimizedUrl($module, $cid = 0, $page = 1) {
        $base_url = rtrim($this->options['site_url'], '/');
        
        if ($cid == 0) {
            // 全站分页
            if ($page > 1) {
                return $base_url . '/' . $module . '/' . $page . '.html';
            } else {
                return $base_url . '/' . $module . '.html';
            }
        } else {
            // 分类分页
            $cate_info = $this->getCategoryInfo($cid);
            if ($cate_info) {
                $url = $base_url . '/' . $module . '/' . $cate_info['cate_dir'] . '/' . $cid;
                if ($page > 1) {
                    $url .= '-' . $page;
                }
                return $url . '.html';
            }
        }
        
        return $base_url . '/';
    }
    
    /**
     * 生成canonical URL
     */
    public function generateCanonicalUrl($module, $cid = 0, $page = 1) {
        return $this->generateOptimizedUrl($module, $cid, $page);
    }
    
    /**
     * 获取分类信息
     */
    private function getCategoryInfo($cid) {
        if ($cid <= 0) return null;
        
        $query = $this->DB->query("SELECT cate_dir, cate_name FROM " . $this->DB->table('categories') . " WHERE cate_id = " . intval($cid));
        return $this->DB->fetch_array($query);
    }
    
    /**
     * 生成301重定向规则
     */
    public function generate301Rules() {
        $rules = [];
        
        // 为webdir生成重定向规则
        $rules[] = '# Webdir分页重定向';
        $rules[] = 'RewriteCond %{QUERY_STRING} ^mod=webdir&cid=0&page=(\d+)$';
        $rules[] = 'RewriteRule ^index\.php$ /webdir/%1.html? [R=301,L]';
        $rules[] = '';
        
        $rules[] = 'RewriteCond %{QUERY_STRING} ^mod=webdir&cid=0$';
        $rules[] = 'RewriteRule ^index\.php$ /webdir.html? [R=301,L]';
        $rules[] = '';
        
        // 为article生成重定向规则
        $rules[] = '# Article分页重定向';
        $rules[] = 'RewriteCond %{QUERY_STRING} ^mod=article&cid=0&page=(\d+)$';
        $rules[] = 'RewriteRule ^index\.php$ /article/%1.html? [R=301,L]';
        $rules[] = '';
        
        $rules[] = 'RewriteCond %{QUERY_STRING} ^mod=article&cid=0$';
        $rules[] = 'RewriteRule ^index\.php$ /article.html? [R=301,L]';
        
        return implode("\n", $rules);
    }
    
    /**
     * 检查URL状态
     */
    public function checkUrlStatus($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Pagination-Optimizer/1.0)');
        
        curl_exec($ch);
        $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return $status_code;
    }
    
    /**
     * 生成sitemap条目
     */
    public function generateSitemapEntries($module) {
        $entries = [];
        $table = ($module == 'webdir') ? 'websites' : 'articles';
        $status_field = ($module == 'webdir') ? 'web_status' : 'art_status';
        
        $total = $this->DB->get_count($this->DB->table($table), "$status_field = 1");
        $pages = ceil($total / 10);
        
        for ($page = 1; $page <= min($pages, 50); $page++) { // 最多50页
            $url = $this->generateOptimizedUrl($module, 0, $page);
            $priority = $page == 1 ? '0.8' : '0.6';
            
            $entries[] = [
                'url' => $url,
                'lastmod' => date('c'),
                'changefreq' => 'daily',
                'priority' => $priority
            ];
        }
        
        return $entries;
    }
}

// 如果直接访问此文件，执行检查
if (basename($_SERVER['PHP_SELF']) == 'pagination_optimizer.php') {
    $optimizer = new PaginationOptimizer();
    
    header('Content-Type: application/json; charset=utf-8');
    
    $action = isset($_GET['action']) ? $_GET['action'] : 'check';
    
    switch ($action) {
        case 'check':
            $issues = $optimizer->checkProblematicUrls();
            echo json_encode([
                'status' => 'success',
                'issues' => $issues,
                'count' => count($issues),
                'message' => '发现 ' . count($issues) . ' 个分页URL问题'
            ]);
            break;
            
        case 'generate_rules':
            $rules = $optimizer->generate301Rules();
            echo json_encode([
                'status' => 'success',
                'rules' => $rules,
                'message' => '301重定向规则生成完成'
            ]);
            break;
            
        case 'sitemap':
            $module = $_GET['module'] ?? 'webdir';
            $entries = $optimizer->generateSitemapEntries($module);
            echo json_encode([
                'status' => 'success',
                'entries' => $entries,
                'count' => count($entries),
                'message' => "为 $module 生成了 " . count($entries) . " 个sitemap条目"
            ]);
            break;
            
        case 'optimize_url':
            $module = $_GET['module'] ?? 'webdir';
            $cid = intval($_GET['cid'] ?? 0);
            $page = intval($_GET['page'] ?? 1);
            
            $optimized = $optimizer->generateOptimizedUrl($module, $cid, $page);
            echo json_encode([
                'status' => 'success',
                'optimized_url' => $optimized,
                'canonical_url' => $optimizer->generateCanonicalUrl($module, $cid, $page)
            ]);
            break;
            
        default:
            echo json_encode([
                'status' => 'error',
                'message' => '未知操作'
            ]);
    }
}
?>
