<?php
/**
 * 分页SEO增强器
 * 专门为保持原有URL格式的分页页面增加SEO优化
 * 处理类似 ?mod=webdir&cid=0&page=2 这样的URL
 */

if (!defined('IN_IWEBDIR')) {
    define('IN_IWEBDIR', true);
}

require_once dirname(__FILE__) . '/../config.php';
require_once dirname(__FILE__) . '/../module/common.php';

class PaginationSEOEnhancer {
    
    private $DB;
    private $options;
    
    public function __construct() {
        global $DB, $options;
        $this->DB = $DB;
        $this->options = $options;
    }
    
    /**
     * 为分页URL生成增强的meta标签
     */
    public function generateEnhancedMeta($module, $cid = 0, $page = 1) {
        $meta = [];
        
        // 基础信息
        $module_name = $this->getModuleName($module);
        $total_items = $this->getTotalItems($module);
        $total_pages = ceil($total_items / 10);
        
        // 生成增强的title
        if ($page > 1) {
            $title = "{$module_name} - 第{$page}页 (共{$total_pages}页) - {$this->options['site_name']}";
        } else {
            $title = "{$module_name} - {$this->options['site_name']}";
        }
        
        // 生成增强的description
        if ($page > 1) {
            $description = "浏览{$module_name}第{$page}页内容，共{$total_items}个优质资源，第{$page}页/{$total_pages}页。{$this->options['site_description']}";
        } else {
            $description = "浏览全部{$module_name}，共{$total_items}个优质资源。{$this->options['site_description']}";
        }
        
        // 生成增强的keywords
        $keywords = $this->options['site_keywords'];
        if ($page > 1) {
            $keywords .= ",第{$page}页,分页浏览,{$module_name}列表";
        }
        
        $meta['title'] = $title;
        $meta['description'] = $description;
        $meta['keywords'] = $keywords;
        
        // 添加分页相关的meta标签
        $meta['pagination'] = $this->generatePaginationMeta($module, $cid, $page, $total_pages);
        
        return $meta;
    }
    
    /**
     * 生成分页相关的meta标签
     */
    private function generatePaginationMeta($module, $cid, $page, $total_pages) {
        $base_url = $this->options['site_url'];
        $pagination = [];
        
        // canonical URL
        $canonical = $base_url . "?mod={$module}&cid={$cid}";
        if ($page > 1) {
            $canonical .= "&page={$page}";
        }
        $pagination['canonical'] = $canonical;
        
        // prev/next链接
        if ($page > 1) {
            $prev_url = $base_url . "?mod={$module}&cid={$cid}";
            if ($page > 2) {
                $prev_url .= "&page=" . ($page - 1);
            }
            $pagination['prev'] = $prev_url;
        }
        
        if ($page < $total_pages) {
            $next_url = $base_url . "?mod={$module}&cid={$cid}&page=" . ($page + 1);
            $pagination['next'] = $next_url;
        }
        
        // 结构化数据
        $pagination['structured_data'] = $this->generateStructuredData($module, $cid, $page, $total_pages);
        
        return $pagination;
    }
    
    /**
     * 生成结构化数据
     */
    private function generateStructuredData($module, $cid, $page, $total_pages) {
        $base_url = $this->options['site_url'];
        $module_name = $this->getModuleName($module);
        
        $structured_data = [
            '@context' => 'https://schema.org',
            '@type' => 'CollectionPage',
            'name' => $module_name . ($page > 1 ? " - 第{$page}页" : ''),
            'description' => "浏览{$module_name}" . ($page > 1 ? "第{$page}页内容" : '全部内容'),
            'url' => $base_url . "?mod={$module}&cid={$cid}" . ($page > 1 ? "&page={$page}" : ''),
            'isPartOf' => [
                '@type' => 'WebSite',
                'name' => $this->options['site_name'],
                'url' => $this->options['site_url']
            ]
        ];
        
        // 添加分页信息
        if ($total_pages > 1) {
            $structured_data['pagination'] = [
                '@type' => 'PaginationInfo',
                'currentPage' => $page,
                'totalPages' => $total_pages,
                'itemsPerPage' => 10
            ];
        }
        
        return $structured_data;
    }
    
    /**
     * 生成面包屑导航
     */
    public function generateBreadcrumb($module, $cid = 0, $page = 1) {
        $breadcrumb = [];
        
        // 首页
        $breadcrumb[] = [
            'name' => $this->options['site_name'],
            'url' => $this->options['site_url']
        ];
        
        // 模块页
        $module_name = $this->getModuleName($module);
        $breadcrumb[] = [
            'name' => $module_name,
            'url' => $this->options['site_url'] . "?mod={$module}"
        ];
        
        // 分页
        if ($page > 1) {
            $breadcrumb[] = [
                'name' => "第{$page}页",
                'url' => $this->options['site_url'] . "?mod={$module}&cid={$cid}&page={$page}"
            ];
        }
        
        return $breadcrumb;
    }
    
    /**
     * 生成分页导航HTML
     */
    public function generatePaginationHTML($module, $cid, $current_page, $total_pages) {
        if ($total_pages <= 1) return '';
        
        $html = '<nav class="pagination-nav" aria-label="分页导航">';
        $html .= '<div class="pagination-info">共 ' . $total_pages . ' 页</div>';
        $html .= '<ul class="pagination-list">';
        
        $base_url = $this->options['site_url'] . "?mod={$module}&cid={$cid}";
        
        // 上一页
        if ($current_page > 1) {
            $prev_url = $current_page > 2 ? $base_url . "&page=" . ($current_page - 1) : $base_url;
            $html .= '<li><a href="' . $prev_url . '" rel="prev">上一页</a></li>';
        }
        
        // 页码
        $start = max(1, $current_page - 5);
        $end = min($total_pages, $current_page + 5);
        
        if ($start > 1) {
            $html .= '<li><a href="' . $base_url . '">1</a></li>';
            if ($start > 2) {
                $html .= '<li><span>...</span></li>';
            }
        }
        
        for ($i = $start; $i <= $end; $i++) {
            if ($i == $current_page) {
                $html .= '<li><span class="current" aria-current="page">' . $i . '</span></li>';
            } else {
                $page_url = $i > 1 ? $base_url . "&page={$i}" : $base_url;
                $html .= '<li><a href="' . $page_url . '">' . $i . '</a></li>';
            }
        }
        
        if ($end < $total_pages) {
            if ($end < $total_pages - 1) {
                $html .= '<li><span>...</span></li>';
            }
            $html .= '<li><a href="' . $base_url . '&page=' . $total_pages . '">' . $total_pages . '</a></li>';
        }
        
        // 下一页
        if ($current_page < $total_pages) {
            $next_url = $base_url . "&page=" . ($current_page + 1);
            $html .= '<li><a href="' . $next_url . '" rel="next">下一页</a></li>';
        }
        
        $html .= '</ul>';
        $html .= '</nav>';
        
        return $html;
    }
    
    /**
     * 获取模块名称
     */
    private function getModuleName($module) {
        $names = [
            'webdir' => '网站目录',
            'article' => '站长资讯',
            'weblink' => '链接交换'
        ];
        
        return $names[$module] ?? $module;
    }
    
    /**
     * 获取总条目数
     */
    private function getTotalItems($module) {
        switch ($module) {
            case 'webdir':
                return $this->DB->get_count($this->DB->table('websites'), 'web_status = 1');
            case 'article':
                return $this->DB->get_count($this->DB->table('articles'), 'art_status = 1');
            default:
                return 0;
        }
    }
    
    /**
     * 生成sitemap条目（保持原URL格式）
     */
    public function generateSitemapEntries($module) {
        $entries = [];
        $total_items = $this->getTotalItems($module);
        $total_pages = ceil($total_items / 10);
        
        for ($page = 1; $page <= min($total_pages, 50); $page++) {
            $url = $this->options['site_url'] . "?mod={$module}&cid=0";
            if ($page > 1) {
                $url .= "&page={$page}";
            }
            
            $entries[] = [
                'url' => $url,
                'lastmod' => date('c'),
                'changefreq' => 'daily',
                'priority' => $page == 1 ? '0.8' : '0.6'
            ];
        }
        
        return $entries;
    }
}

// 如果直接访问此文件，执行相关操作
if (basename($_SERVER['PHP_SELF']) == 'pagination_seo_enhancer.php') {
    $enhancer = new PaginationSEOEnhancer();
    
    header('Content-Type: application/json; charset=utf-8');
    
    $action = isset($_GET['action']) ? $_GET['action'] : 'meta';
    $module = isset($_GET['module']) ? $_GET['module'] : 'webdir';
    $cid = isset($_GET['cid']) ? intval($_GET['cid']) : 0;
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    
    switch ($action) {
        case 'meta':
            $meta = $enhancer->generateEnhancedMeta($module, $cid, $page);
            echo json_encode([
                'status' => 'success',
                'meta' => $meta
            ]);
            break;
            
        case 'breadcrumb':
            $breadcrumb = $enhancer->generateBreadcrumb($module, $cid, $page);
            echo json_encode([
                'status' => 'success',
                'breadcrumb' => $breadcrumb
            ]);
            break;
            
        case 'pagination_html':
            $total_pages = ceil($enhancer->getTotalItems($module) / 10);
            $html = $enhancer->generatePaginationHTML($module, $cid, $page, $total_pages);
            echo json_encode([
                'status' => 'success',
                'html' => $html
            ]);
            break;
            
        case 'sitemap':
            $entries = $enhancer->generateSitemapEntries($module);
            echo json_encode([
                'status' => 'success',
                'entries' => $entries,
                'count' => count($entries)
            ]);
            break;
            
        default:
            echo json_encode([
                'status' => 'error',
                'message' => '未知操作'
            ]);
    }
}
?>
