/**
 * 增强分页样式
 * 专门为保持原有URL格式的分页页面设计
 */

/* 分页导航容器 */
.pagination-nav {
    margin: 30px 0;
    text-align: center;
    clear: both;
}

/* 分页信息 */
.pagination-info {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}

.pagination-info .current-range {
    font-weight: bold;
    color: #2c7be5;
}

/* 分页列表 */
.pagination-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    flex-wrap: wrap;
    justify-content: center;
}

.pagination-list li {
    margin: 0;
}

.pagination-list a,
.pagination-list span {
    display: inline-block;
    padding: 8px 12px;
    text-decoration: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    background: #fff;
    transition: all 0.3s ease;
    min-width: 40px;
    text-align: center;
}

.pagination-list a:hover {
    background: #2c7be5;
    color: #fff;
    border-color: #2c7be5;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(44, 123, 229, 0.3);
}

/* 当前页样式 */
.pagination-list .current {
    background: #2c7be5;
    color: #fff;
    border-color: #2c7be5;
    font-weight: bold;
    cursor: default;
}

/* 省略号样式 */
.pagination-list span:not(.current) {
    background: transparent;
    border: none;
    color: #999;
    cursor: default;
}

/* 上一页/下一页按钮 */
.pagination-list .prev-next {
    font-weight: bold;
    padding: 8px 16px;
}

.pagination-list .prev-next:hover {
    background: #1c5bb7;
}

/* 首页/末页按钮 */
.pagination-list .first-last {
    background: #f8f9fa;
    color: #6c757d;
}

.pagination-list .first-last:hover {
    background: #e9ecef;
    color: #495057;
}

/* 分页统计信息 */
.pagination-stats {
    margin-top: 15px;
    font-size: 13px;
    color: #888;
}

.pagination-stats .highlight {
    color: #2c7be5;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .pagination-list {
        gap: 3px;
    }
    
    .pagination-list a,
    .pagination-list span {
        padding: 6px 8px;
        font-size: 14px;
        min-width: 32px;
    }
    
    .pagination-list .prev-next {
        padding: 6px 12px;
    }
    
    /* 在小屏幕上隐藏部分页码 */
    .pagination-list .hide-mobile {
        display: none;
    }
}

@media (max-width: 480px) {
    .pagination-nav {
        margin: 20px 0;
    }
    
    .pagination-list {
        gap: 2px;
    }
    
    .pagination-list a,
    .pagination-list span {
        padding: 5px 6px;
        font-size: 13px;
        min-width: 28px;
    }
}

/* 分页导航增强功能 */
.pagination-enhanced {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 30px 0;
}

.pagination-enhanced .page-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.pagination-enhanced .page-summary {
    font-size: 14px;
    color: #666;
}

.pagination-enhanced .page-jump {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.pagination-enhanced .page-jump input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.pagination-enhanced .page-jump button {
    padding: 4px 12px;
    background: #2c7be5;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.pagination-enhanced .page-jump button:hover {
    background: #1c5bb7;
}

/* SEO增强提示 */
.seo-pagination-info {
    background: #e8f4fd;
    border-left: 4px solid #2c7be5;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 4px 4px 0;
}

.seo-pagination-info .title {
    font-weight: bold;
    color: #2c7be5;
    margin-bottom: 8px;
}

.seo-pagination-info .description {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

/* 面包屑导航增强 */
.breadcrumb-enhanced {
    background: #fff;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.breadcrumb-enhanced .breadcrumb-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb-enhanced .breadcrumb-list li {
    display: flex;
    align-items: center;
}

.breadcrumb-enhanced .breadcrumb-list li:not(:last-child)::after {
    content: "›";
    margin: 0 8px;
    color: #ccc;
}

.breadcrumb-enhanced .breadcrumb-list a {
    color: #2c7be5;
    text-decoration: none;
    transition: color 0.3s;
}

.breadcrumb-enhanced .breadcrumb-list a:hover {
    color: #1c5bb7;
    text-decoration: underline;
}

.breadcrumb-enhanced .breadcrumb-list .current {
    color: #666;
    font-weight: bold;
}

/* 加载动画 */
.pagination-loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.pagination-loading::after {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #2c7be5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
