/**
 * 分页增强功能
 * 为保持原有URL格式的分页页面提供增强功能
 */

class PaginationEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.addPageJumpFeature();
        this.addKeyboardNavigation();
        this.addScrollToTop();
        this.addLoadingStates();
        this.enhanceSEOInfo();
        this.addAnalytics();
    }

    /**
     * 添加页面跳转功能
     */
    addPageJumpFeature() {
        const paginationNav = document.querySelector('.pagination-nav');
        if (!paginationNav) return;

        const currentPage = this.getCurrentPage();
        const totalPages = this.getTotalPages();
        
        if (totalPages <= 1) return;

        const jumpHTML = `
            <div class="page-jump-container" style="margin-top: 15px;">
                <span>跳转到第</span>
                <input type="number" id="pageJumpInput" min="1" max="${totalPages}" 
                       value="${currentPage}" style="width: 60px; margin: 0 5px; padding: 4px; text-align: center;">
                <span>页</span>
                <button id="pageJumpBtn" style="margin-left: 10px; padding: 4px 12px; background: #2c7be5; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    跳转
                </button>
                <span style="margin-left: 10px; color: #666; font-size: 12px;">共 ${totalPages} 页</span>
            </div>
        `;

        paginationNav.insertAdjacentHTML('beforeend', jumpHTML);

        // 绑定跳转事件
        const jumpBtn = document.getElementById('pageJumpBtn');
        const jumpInput = document.getElementById('pageJumpInput');

        jumpBtn.addEventListener('click', () => this.jumpToPage());
        jumpInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.jumpToPage();
        });
    }

    /**
     * 跳转到指定页面
     */
    jumpToPage() {
        const input = document.getElementById('pageJumpInput');
        const page = parseInt(input.value);
        const totalPages = this.getTotalPages();

        if (page < 1 || page > totalPages || isNaN(page)) {
            alert(`请输入1到${totalPages}之间的页码`);
            return;
        }

        const currentUrl = new URL(window.location);
        const module = currentUrl.searchParams.get('mod') || 'webdir';
        const cid = currentUrl.searchParams.get('cid') || '0';

        let newUrl = `?mod=${module}&cid=${cid}`;
        if (page > 1) {
            newUrl += `&page=${page}`;
        }

        window.location.href = newUrl;
    }

    /**
     * 添加键盘导航
     */
    addKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // 只在没有焦点在输入框时响应
            if (document.activeElement.tagName === 'INPUT') return;

            const currentPage = this.getCurrentPage();
            const totalPages = this.getTotalPages();

            if (e.key === 'ArrowLeft' && currentPage > 1) {
                // 左箭头 - 上一页
                this.navigateToPage(currentPage - 1);
            } else if (e.key === 'ArrowRight' && currentPage < totalPages) {
                // 右箭头 - 下一页
                this.navigateToPage(currentPage + 1);
            } else if (e.key === 'Home') {
                // Home键 - 首页
                e.preventDefault();
                this.navigateToPage(1);
            } else if (e.key === 'End') {
                // End键 - 末页
                e.preventDefault();
                this.navigateToPage(totalPages);
            }
        });
    }

    /**
     * 导航到指定页面
     */
    navigateToPage(page) {
        const currentUrl = new URL(window.location);
        const module = currentUrl.searchParams.get('mod') || 'webdir';
        const cid = currentUrl.searchParams.get('cid') || '0';

        let newUrl = `?mod=${module}&cid=${cid}`;
        if (page > 1) {
            newUrl += `&page=${page}`;
        }

        window.location.href = newUrl;
    }

    /**
     * 添加滚动到顶部功能
     */
    addScrollToTop() {
        // 在分页链接点击时滚动到顶部
        const paginationLinks = document.querySelectorAll('.pagination-list a');
        paginationLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // 添加平滑滚动
                setTimeout(() => {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                }, 100);
            });
        });
    }

    /**
     * 添加加载状态
     */
    addLoadingStates() {
        const paginationLinks = document.querySelectorAll('.pagination-list a');
        paginationLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // 添加加载状态
                link.style.opacity = '0.6';
                link.style.pointerEvents = 'none';
                link.innerHTML += ' <span class="loading">...</span>';
            });
        });
    }

    /**
     * 增强SEO信息显示
     */
    enhanceSEOInfo() {
        const currentPage = this.getCurrentPage();
        const totalPages = this.getTotalPages();
        const module = this.getCurrentModule();

        if (currentPage > 1) {
            // 添加SEO信息提示
            const seoInfo = document.createElement('div');
            seoInfo.className = 'seo-pagination-info';
            seoInfo.innerHTML = `
                <div class="title">📄 当前浏览</div>
                <div class="description">
                    您正在浏览${this.getModuleName(module)}的第 ${currentPage} 页，
                    共 ${totalPages} 页内容。使用键盘左右箭头键可快速翻页。
                </div>
            `;

            // 插入到内容区域前
            const contentArea = document.querySelector('.main-content') || document.querySelector('.content');
            if (contentArea) {
                contentArea.insertBefore(seoInfo, contentArea.firstChild);
            }
        }
    }

    /**
     * 添加分析统计
     */
    addAnalytics() {
        const currentPage = this.getCurrentPage();
        const module = this.getCurrentModule();

        // 发送页面浏览统计
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_view', {
                'page_title': `${this.getModuleName(module)} - 第${currentPage}页`,
                'page_location': window.location.href,
                'custom_parameter': {
                    'module': module,
                    'page': currentPage
                }
            });
        }

        // 记录用户行为
        this.trackUserBehavior();
    }

    /**
     * 跟踪用户行为
     */
    trackUserBehavior() {
        let startTime = Date.now();

        // 记录页面停留时间
        window.addEventListener('beforeunload', () => {
            const stayTime = Date.now() - startTime;
            if (stayTime > 5000) { // 停留超过5秒才记录
                this.sendAnalytics('page_stay_time', {
                    duration: stayTime,
                    page: this.getCurrentPage(),
                    module: this.getCurrentModule()
                });
            }
        });

        // 记录分页点击
        const paginationLinks = document.querySelectorAll('.pagination-list a');
        paginationLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const targetPage = this.extractPageFromUrl(link.href);
                this.sendAnalytics('pagination_click', {
                    from_page: this.getCurrentPage(),
                    to_page: targetPage,
                    module: this.getCurrentModule()
                });
            });
        });
    }

    /**
     * 发送分析数据
     */
    sendAnalytics(event, data) {
        // 可以发送到自己的分析服务
        if (window.fetch) {
            fetch('/system/analytics.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event: event,
                    data: data,
                    timestamp: Date.now(),
                    url: window.location.href
                })
            }).catch(e => console.log('Analytics error:', e));
        }
    }

    /**
     * 获取当前页码
     */
    getCurrentPage() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('page')) || 1;
    }

    /**
     * 获取总页数
     */
    getTotalPages() {
        // 从页面中提取总页数信息
        const paginationList = document.querySelector('.pagination-list');
        if (!paginationList) return 1;

        const pageLinks = paginationList.querySelectorAll('a');
        let maxPage = 1;

        pageLinks.forEach(link => {
            const page = this.extractPageFromUrl(link.href);
            if (page > maxPage) maxPage = page;
        });

        return maxPage;
    }

    /**
     * 获取当前模块
     */
    getCurrentModule() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('mod') || 'webdir';
    }

    /**
     * 获取模块名称
     */
    getModuleName(module) {
        const names = {
            'webdir': '网站目录',
            'article': '站长资讯',
            'weblink': '链接交换'
        };
        return names[module] || module;
    }

    /**
     * 从URL中提取页码
     */
    extractPageFromUrl(url) {
        const urlObj = new URL(url, window.location.origin);
        return parseInt(urlObj.searchParams.get('page')) || 1;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PaginationEnhancer();
});

// 添加一些实用的全局函数
window.PaginationUtils = {
    jumpToPage: function(page) {
        const enhancer = new PaginationEnhancer();
        enhancer.navigateToPage(page);
    },
    
    getCurrentPageInfo: function() {
        const enhancer = new PaginationEnhancer();
        return {
            currentPage: enhancer.getCurrentPage(),
            totalPages: enhancer.getTotalPages(),
            module: enhancer.getCurrentModule()
        };
    }
};
